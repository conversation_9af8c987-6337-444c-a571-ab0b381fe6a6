// components/VendorPMIContacts.tsx
import { useState, useMemo } from "react";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Snackbar,
  TextField,
  Typography,
  Alert,
  Box,
  MenuItem,
  IconButton,
} from "@mui/material";
import { Add, Delete, Edit } from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuthStore } from "../../../store/auth";
import Table, { ColumnType } from "../../../components/Table";
import {
  createVendorPMIContact,
  deleteVendorPMIContact,
  getDepartments,
  getMapContactByVendorId,
  updateVendorPMIContact,
  VendorPMIContact,
} from "../../../services/pmi/vendorPMIContacts";
import Loader from "../../../utils/Loader";
import { getAllContacts } from "../../../services/contact/contact";
import { useLocation, useNavigate, useParams } from "react-router-dom";

function VendorPMIContacts() {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { user } = useAuthStore();
  const params = useParams();
  const { id } = params;
  const [open, setOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedContact, setSelectedContact] = useState<any>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [contactToDelete, setContactToDelete] = useState<any>(null);
  const navigate = useNavigate();
  const location = useLocation();

  const [formData, setFormData] = useState({
    vendor_id: Number(id),
    department_id: 0,
    pmi_contact_ids: [] as number[],
    updated_by: String(user?.id) || "",
  });

  const [formErrors, setFormErrors] = useState({
    pmi_contact_ids: "",
    department_id: "",
  });

  const [generalError, setGeneralError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success" as "success" | "error",
  });

  const [page, setPage] = useState(0);
  const [pageCount, setPageCount] = useState(10);
  const [sort, setSort] = useState<any>({});

  const { data: contactsResponse, isLoading } = useQuery({
    queryKey: ["contacts", id, page, pageCount, sort],
    queryFn: ({ queryKey }) => getMapContactByVendorId(queryKey[1]),
    enabled: !!id,
  });

  const contactsData = useMemo(() => {
    const rawData = contactsResponse?.data || [];
    if (!Array.isArray(rawData)) return [];
    return rawData.map((item: any) => ({
      id: item.id,
      vendor_id: item.vendor?.id,
      department_id: item.department?.id,
      pmi_contact_id: item.pmiContacts?.[0]?.id,
      pmi_contact: item.pmiContacts?.[0] || null,
      departments: item.department ? [item.department] : [],
    }));
  }, [contactsResponse]);

  const totalCount = contactsResponse?.totalCount || 0;
  const { data: internalContacts } = useQuery({
    queryKey: ["internalContacts", page, pageCount, sort],
    queryFn: getAllContacts,
  });
  const InternalcontactsData = internalContacts?.result || [];

  const { data: departments = [] } = useQuery({
    queryKey: ["departments"],
    queryFn: getDepartments,
  });

  const createMutation = useMutation({
    mutationFn: createVendorPMIContact,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contacts"] });
      setSnackbar({ open: true, message: t("pmicontacts.contactCreated"), severity: "success" });
      setOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      setGeneralError(error.response?.data?.message || t("common.error"));
    },
  });

 const updateMutation = useMutation({
  mutationFn: (payload: {
    id: number;
    vendor_id: number;
    department_id: number;
    pmi_contact_ids: number[];
    updated_by: string;
  }) => updateVendorPMIContact(payload),
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ["contacts"] });
    setSnackbar({ open: true, message: t("pmicontacts.contactUpdated"), severity: "success" });
    setOpen(false);
    resetForm();
  },
  onError: (error: any) => {
    setGeneralError(error.response?.data?.message || t("common.error"));
  },
});


  const deleteMutation = useMutation({
    mutationFn: deleteVendorPMIContact,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contacts"] });
      setSnackbar({ open: true, message: t("pmicontacts.contactDeleted"), severity: "success" });
      setDeleteDialogOpen(false);
      setContactToDelete(null);
    },
    onError: (error: any) => {
      setSnackbar({ open: true, message: error.response?.data?.message || t("common.error"), severity: "error" });
    },
  });

  const defaultColumns: ColumnType[] = useMemo(() => [
    {
      title: t("pmicontacts.department"),
      key: "department_name",
      render: (item: any) => item.departments?.map((d: any) => d.department_name).join(", ") || "-",
    },
    {
      title: t("pmicontacts.internalContact"),
      key: "contact_id",
      render: (item: any) => {
        const c = item.pmi_contact;
        return c ? `${c.first_name} ${c.last_name} (${c.email})` : "-";
      },
    },
    {
      title: t("common.actions"),
      key: "actions",
      render: (item: any) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton size="small" onClick={() => handleEdit(item)} color="primary">
            <Edit fontSize="small" />
          </IconButton>
          <IconButton size="small" onClick={() => handleDeleteClick(item)} color="error">
            <Delete fontSize="small" />
          </IconButton>
        </Box>
      ),
    },
  ], [t]);

  const handleEdit = (contact: any) => {
    setSelectedContact(contact);
    setFormData({
      vendor_id: contact.vendor_id,
      department_id: contact.department_id,
      pmi_contact_ids: [contact.pmi_contact_id],
      updated_by: String(user?.id) || "",
    });
    setEditMode(true);
    setOpen(true);
    setGeneralError("");
    setFormErrors({ pmi_contact_ids: "", department_id: "" });
  };

  const handleDeleteClick = (contact: any) => {
    setContactToDelete(contact);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (contactToDelete) {
      deleteMutation.mutate(contactToDelete.id);
    }
  };

  const resetForm = () => {
    setFormData({
      vendor_id: Number(id),
      department_id: 0,
      pmi_contact_ids: [],
      updated_by: String(user?.id) || "",
    });
    setFormErrors({ pmi_contact_ids: "", department_id: "" });
    setGeneralError("");
    setEditMode(false);
    setSelectedContact(null);
  };

const handleSubmit = () => {
  setGeneralError("");
  const errors = {
    pmi_contact_ids: formData.pmi_contact_ids.length ? "" : t("pmicontacts.contactRequired"),
    department_id: formData.department_id ? "" : t("pmicontacts.departmentRequired"),
  };
  setFormErrors(errors);
  if (errors.pmi_contact_ids || errors.department_id) return;

  setIsSubmitting(true);

  if (editMode && selectedContact) {
    const payload:any = {
      id: selectedContact.id,
      department_id: formData.department_id,
      pmi_contact_ids: formData.pmi_contact_ids,
      updated_by: formData.updated_by,
    };
    updateMutation.mutate(payload);
  } else {
    const payload:any = {
      vendor_id: formData.vendor_id,
      department_id: formData.department_id,
      pmi_contact_ids: formData.pmi_contact_ids,
    };
    createMutation.mutate(payload);
  }

  setIsSubmitting(false);
};

  if (isLoading) return <Loader value="Loading..." />;

  if (user?.role !== "business") {
    return (
      <Box p={3} textAlign="center">
        <Typography variant="h6" color="error">
          {t("systemPreferences.permissionDenied")}
        </Typography>
      </Box>
    );
  }

  const openChangeHistory = () => {
    navigate(`/pmi-mapcontacts-change-history/${formData.vendor_id}`, {
      state: { backgroundLocation: location },
    });
  };

  return (
    <>
      <Box m={2}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h4">{t("pmicontacts.maptitle")}</Typography>
          <Box sx={{ display: "flex", gap: 2 }}>
            <Button variant="contained" startIcon={<Add />} onClick={() => { resetForm(); setOpen(true); }}>
              {t("pmicontacts.maptitle")}
            </Button>
            <Button variant="contained" color="primary" onClick={openChangeHistory}>
              {t("systemPreferences.changeHistory")}
            </Button>
          </Box>
        </Box>

        <Table
          sx={{ height: "auto" }}
          columns={defaultColumns}
          data={contactsData || []}
          pagination={{ totalCount, pageCount, setPageCount, page, setPage }}
          enableSearch={true}
          enableFilter={true}
          searchPlaceholder={t("table.searchPlaceholder")}
          loading={isLoading}
        />
      </Box>

      <Dialog open={open} onClose={() => setOpen(false)} fullWidth maxWidth="sm">
        <DialogTitle>{editMode ? t("pmicontacts.editContact") : t("pmicontacts.maptitle")}</DialogTitle>
        <DialogContent dividers>
          {generalError && <Alert severity="error">{generalError}</Alert>}
          <TextField
            select
            label={t("pmicontacts.department")}
            fullWidth
            margin="dense"
            value={formData.department_id || ""}
            onChange={(e) => setFormData({ ...formData, department_id: Number(e.target.value) })}
            error={!!formErrors.department_id}
            helperText={formErrors.department_id}
            sx={{ mb: 2 }}
          >
            {departments.map((dept) => (
              <MenuItem key={dept.id} value={dept.id}>{dept.department_name}</MenuItem>
            ))}
          </TextField>
          <TextField
            select
            label={t("pmicontacts.internalContact")}
            fullWidth
            margin="dense"
            value={formData.pmi_contact_ids[0] || ""}
            onChange={(e) => setFormData({ ...formData, pmi_contact_ids: [Number(e.target.value)] })}
            error={!!formErrors.pmi_contact_ids}
            helperText={formErrors.pmi_contact_ids}
            sx={{ mb: 2 }}
          >
            {InternalcontactsData?.map((contact) => (
              <MenuItem key={contact.id} value={contact.id}>
                {contact.first_name} {contact.last_name} ({contact.email})
              </MenuItem>
            ))}
          </TextField>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)} color="inherit">{t("common.cancel")}</Button>
          <Button onClick={handleSubmit} variant="contained" disabled={isSubmitting}>
            {isSubmitting ? t("common.loading") : editMode ? t("common.update") : t("common.create")}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{t("pmicontacts.deleteContact")}</DialogTitle>
        <DialogContent>
          <Typography>{t("pmicontacts.confirmDeleteContact")}</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)} color="inherit">{t("common.cancel")}</Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained" disabled={deleteMutation.isPending}>
            {deleteMutation.isPending ? t("common.loading") : t("common.delete")}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert onClose={() => setSnackbar({ ...snackbar, open: false })} severity={snackbar.severity} sx={{ width: "100%" }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
}

export default VendorPMIContacts;
